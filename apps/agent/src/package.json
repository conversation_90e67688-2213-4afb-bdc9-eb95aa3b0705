{"name": "gauzy-agent", "productName": "Gauzy Agent", "version": "0.1.0", "description": "Gauzy Agent", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "main": "index.js", "workspaces": {"packages": ["../../../dist/packages/contracts", "../../../dist/packages/desktop-core", "../../../dist/packages/desktop-activity", "../../../dist/packages/desktop-lib", "../../../dist/packages/desktop-window", "../../../dist/packages/ui-config", "../../../dist/packages/constants"]}, "build": {"appId": "com.ever.gauzyagent", "artifactName": "${name}-${version}.${ext}", "productName": "Gauzy Agent", "copyright": "Copyright © 2019-Present. Ever Co. LTD", "afterSign": "tools/notarize.js", "dmg": {"sign": false}, "asar": true, "npmRebuild": true, "asarUnpack": [], "directories": {"buildResources": "icons", "output": "../agent-packages"}, "publish": [{"provider": "github", "repo": "ever-agent", "releaseType": "release"}, {"provider": "spaces", "name": "ever", "region": "sfo3", "path": "/ever-agent", "acl": "public-read"}], "mac": {"category": "public.app-category.developer-tools", "icon": "icon.icns", "target": ["zip", "dmg"], "asarUnpack": "**/*.node", "artifactName": "${name}-${version}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "tools/build/entitlements.mas.plist", "entitlementsInherit": "tools/build/entitlements.mas.plist", "extendInfo": {"NSAppleEventsUsageDescription": "Please allow access to script browser applications to detect the current URL when triggering instant lookup.", "NSCameraUsageDescription": "Please allow access to Gauzy Agent"}}, "win": {"publisherName": "Ever", "target": [{"target": "nsis", "arch": ["x64"]}], "icon": "icon.ico", "verifyUpdateCodeSignature": false}, "linux": {"icon": "linux", "target": ["AppImage", "deb", "tar.gz"], "executableName": "ever-gauzy-agent", "artifactName": "${name}-${version}.${ext}", "synopsis": "Desktop", "category": "Development"}, "nsis": {"oneClick": false, "perMachine": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "icon.ico", "artifactName": "${name}-${version}.${ext}", "deleteAppDataOnUninstall": true, "menuCategory": true}, "extraResources": ["./data/**/*", "databaseDir", {"from": "assets", "to": "assets"}]}, "dependencies": {"electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-util": "^0.18.1", "node-fetch": "^2.6.7", "node-notifier": "^8.0.0", "@gauzy/contracts": "file:../../../dist/packages/contracts", "@gauzy/desktop-core": "file:../../../dist/packages/desktop-core", "@gauzy/desktop-activity": "file:../../../dist/packages/desktop-activity", "@gauzy/desktop-lib": "file:../../../dist/packages/desktop-lib", "@gauzy/desktop-window": "file:../../../dist/packages/desktop-window", "@gauzy/ui-config": "file:../../../dist/packages/ui-config", "@gauzy/constants": "file:../../../dist/packages/constants", "@electron/remote": "^2.0.8", "@sentry/electron": "6.9.0", "@sentry/profiling-node": "9.43.0", "@sentry/node": "9.43.0", "@sentry/core": "9.43.0", "auto-launch": "5.0.5", "custom-electron-titlebar": "^4.2.8", "form-data": "^4.0.1"}}